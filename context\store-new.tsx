"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import {
  Device,
  Contact,
  Warehouse,
  Sale,
  SaleItem,
  Return,
  DeviceStatus,
  Manufacturer,
  DeviceModel,
  SupplyOrder,
  EvaluationOrder,
  MaintenanceLog,
  MaintenanceResult,
  WarehouseTransfer,
  User,
  SystemSettings,
  EmployeeRequest,
  EmployeeRequestStatus,
  InternalMessage,
  MessageStatus,
  AcceptanceOrder,
  MaintenanceOrder,
  DeliveryOrder,
  DeliveryOrderItem,
  MaintenanceReceiptOrder,
  DeviceReturnHistory,
  Stocktake,
  StocktakeDiscrepancy,
  StocktakeStatus,
  StocktakeFilter,
  StocktakeV1,
  StocktakeItemV1,
  AppPermissions,
  permissionPages,
  PaginatedResponse,
  ApiQueryParams,
  LoadingState,
} from "@/lib/types";

// استيراد دوال جلب البيانات الجديدة
import {
  fetchDevices,
  fetchSales,
  fetchSupplyOrders,
  fetchReturns,
  fetchClients,
  fetchSuppliers,
  fetchWarehouses,
  fetchUsers,
  fetchEvaluationOrders,
  fetchMaintenanceOrders,
  fetchDeliveryOrders,
  fetchInternalMessages,
  dataFetcher
} from '@/lib/data-fetcher';

import { globalCache, staticDataCache, dynamicDataCache } from '@/lib/cache-manager';
import { apiClient, handleApiResponse } from "@/lib/api-client";

// --- STORE CONTEXT INTERFACE ---

interface StoreContextType {
  // --- LOADING STATES ---
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  
  // --- USER AND AUTH ---
  currentUser: User | null;
  setCurrentUser: (user: User | null) => void;
  users: User[];
  
  // --- ON-DEMAND DATA FETCHING ---
  fetchDevicesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Device>>;
  fetchSalesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Sale>>;
  fetchSupplyOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<SupplyOrder>>;
  fetchReturnsData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Return>>;
  fetchClientsData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Contact>>;
  fetchSuppliersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Contact>>;
  fetchWarehousesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Warehouse>>;
  fetchUsersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<User>>;
  fetchEvaluationOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<EvaluationOrder>>;
  fetchMaintenanceOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<MaintenanceOrder>>;
  fetchDeliveryOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<DeliveryOrder>>;
  fetchInternalMessagesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<InternalMessage>>;
  
  // --- LOADING STATES FOR EACH DATA TYPE ---
  getLoadingState: (dataType: string, params?: ApiQueryParams) => LoadingState;
  
  // --- CACHE MANAGEMENT ---
  invalidateCache: (dataType: string, params?: ApiQueryParams) => void;
  clearAllCache: () => void;
  getCacheStats: () => any;
  
  // --- LEGACY DATA (for backward compatibility) ---
  devices: Device[];
  sales: Sale[];
  supplyOrders: SupplyOrder[];
  returns: Return[];
  clients: Contact[];
  suppliers: Contact[];
  warehouses: Warehouse[];
  evaluationOrders: EvaluationOrder[];
  maintenanceOrders: MaintenanceOrder[];
  deliveryOrders: DeliveryOrder[];
  internalMessages: InternalMessage[];
  
  // --- CRUD OPERATIONS (updated to work with new system) ---
  addDevice: (device: Device) => Promise<void>;
  updateDevice: (device: Device) => Promise<void>;
  deleteDevice: (deviceId: string) => Promise<void>;
  
  addSale: (sale: Sale) => Promise<void>;
  updateSale: (sale: Sale) => Promise<void>;
  deleteSale: (saleId: number) => Promise<void>;
  
  addSupplyOrder: (order: SupplyOrder) => Promise<void>;
  updateSupplyOrder: (order: SupplyOrder) => Promise<void>;
  deleteSupplyOrder: (orderId: number) => Promise<void>;
  
  addReturn: (returnOrder: Return) => Promise<void>;
  updateReturn: (returnOrder: Return) => Promise<void>;
  deleteReturn: (returnId: number) => Promise<void>;
  
  addClient: (client: Contact) => Promise<void>;
  updateClient: (client: Contact) => Promise<void>;
  deleteClient: (clientId: number) => Promise<void>;
  
  addSupplier: (supplier: Contact) => Promise<void>;
  updateSupplier: (supplier: Contact) => Promise<void>;
  deleteSupplier: (supplierId: number) => Promise<void>;
  
  addWarehouse: (warehouse: Warehouse) => Promise<void>;
  updateWarehouse: (warehouse: Warehouse) => Promise<void>;
  deleteWarehouse: (warehouseId: number) => Promise<void>;
  
  // --- UTILITY FUNCTIONS ---
  getDeviceById: (deviceId: string) => Device | undefined;
  getWarehouseById: (warehouseId: number) => Warehouse | undefined;
  getClientById: (clientId: number) => Contact | undefined;
  getSupplierById: (supplierId: number) => Contact | undefined;
  getUserById: (userId: number) => User | undefined;
  
  // --- SYSTEM SETTINGS ---
  systemSettings: SystemSettings;
  updateSystemSettings: (settings: Partial<SystemSettings>) => Promise<void>;
  
  // --- MAINTENANCE AND OTHER LEGACY FEATURES ---
  maintenanceReceiptOrders: MaintenanceReceiptOrder[];
  stocktakes: StocktakeV1[];
  
  // Legacy functions that will be gradually migrated
  loadDataFromAPIs: () => Promise<void>;
}

// --- STORE CONTEXT ---

const StoreContext = createContext<StoreContextType | undefined>(undefined);

export const useStore = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error("useStore must be used within a StoreProvider");
  }
  return context;
};

// --- STORE PROVIDER ---

interface StoreProviderProps {
  children: ReactNode;
}

export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  // --- STATE MANAGEMENT ---
  const [isLoading, setIsLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  
  // Legacy state for backward compatibility
  const [users, setUsers] = useState<User[]>([]);
  const [devices, setDevices] = useState<Device[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [supplyOrders, setSupplyOrders] = useState<SupplyOrder[]>([]);
  const [returns, setReturns] = useState<Return[]>([]);
  const [clients, setClients] = useState<Contact[]>([]);
  const [suppliers, setSuppliers] = useState<Contact[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [evaluationOrders, setEvaluationOrders] = useState<EvaluationOrder[]>([]);
  const [maintenanceOrders, setMaintenanceOrders] = useState<MaintenanceOrder[]>([]);
  const [deliveryOrders, setDeliveryOrders] = useState<DeliveryOrder[]>([]);
  const [internalMessages, setInternalMessages] = useState<InternalMessage[]>([]);
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState<MaintenanceReceiptOrder[]>([]);
  const [stocktakes, setStocktakes] = useState<StocktakeV1[]>([]);
  
  // System settings
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    companyName: "شركة الأجهزة الذكية",
    companyAddress: "الرياض، المملكة العربية السعودية",
    companyPhone: "+966 11 123 4567",
    companyEmail: "<EMAIL>",
    taxNumber: "*********",
    currency: "ريال سعودي",
    language: "ar",
    dateFormat: "DD/MM/YYYY",
    timeFormat: "24h",
    timezone: "Asia/Riyadh",
    backupFrequency: "daily",
    maxFileSize: 10,
    allowedFileTypes: ["pdf", "jpg", "png", "docx"],
    sessionTimeout: 30,
    passwordMinLength: 8,
    requirePasswordChange: false,
    enableTwoFactor: false,
    enableAuditLog: true,
    enableNotifications: true,
    defaultWarehouseId: 1,
    defaultCurrency: "SAR",
    taxRate: 15,
    invoicePrefix: "INV",
    receiptPrefix: "REC",
    orderPrefix: "ORD"
  });

  // --- INITIALIZATION ---
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Load essential data only (users for authentication)
      loadEssentialData();
    }
  }, []);

  // Load only essential data for app initialization
  const loadEssentialData = async () => {
    try {
      setIsLoading(true);
      console.log("تحميل البيانات الأساسية...");

      // Load users only for authentication
      const usersData = await fetchUsersData({ pagination: { limit: 100 } });
      setUsers(usersData.data);
      
      console.log("تم تحميل البيانات الأساسية بنجاح");
    } catch (error) {
      console.error("خطأ في تحميل البيانات الأساسية:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // --- ON-DEMAND DATA FETCHING FUNCTIONS ---
  
  const fetchDevicesData = async (params: ApiQueryParams = {}) => {
    return await fetchDevices(params);
  };

  const fetchSalesData = async (params: ApiQueryParams = {}) => {
    return await fetchSales(params);
  };

  const fetchSupplyOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchSupplyOrders(params);
  };

  const fetchReturnsData = async (params: ApiQueryParams = {}) => {
    return await fetchReturns(params);
  };

  const fetchClientsData = async (params: ApiQueryParams = {}) => {
    return await fetchClients(params);
  };

  const fetchSuppliersData = async (params: ApiQueryParams = {}) => {
    return await fetchSuppliers(params);
  };

  const fetchWarehousesData = async (params: ApiQueryParams = {}) => {
    return await fetchWarehouses(params);
  };

  const fetchUsersData = async (params: ApiQueryParams = {}) => {
    return await fetchUsers(params);
  };

  const fetchEvaluationOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchEvaluationOrders(params);
  };

  const fetchMaintenanceOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchMaintenanceOrders(params);
  };

  const fetchDeliveryOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchDeliveryOrders(params);
  };

  const fetchInternalMessagesData = async (params: ApiQueryParams = {}) => {
    return await fetchInternalMessages(params);
  };

  // --- LOADING STATE MANAGEMENT ---
  
  const getLoadingState = (dataType: string, params: ApiQueryParams = {}): LoadingState => {
    const cacheKey = `${dataType}|${JSON.stringify(params)}`;
    return dataFetcher.getLoadingState(cacheKey);
  };

  // --- CACHE MANAGEMENT ---
  
  const invalidateCache = (dataType: string, params: ApiQueryParams = {}) => {
    const endpoint = getEndpointForDataType(dataType);
    if (endpoint) {
      dataFetcher.invalidateCache(endpoint, params);
    }
  };

  const clearAllCache = () => {
    dataFetcher.clearAllCache();
  };

  const getCacheStats = () => {
    return {
      global: globalCache.getStats(),
      static: staticDataCache.getStats(),
      dynamic: dynamicDataCache.getStats()
    };
  };

  // Helper function to map data types to endpoints
  const getEndpointForDataType = (dataType: string): string | null => {
    const mapping: Record<string, string> = {
      'devices': '/api/devices',
      'sales': '/api/sales',
      'supply': '/api/supply',
      'returns': '/api/returns',
      'clients': '/api/clients',
      'suppliers': '/api/suppliers',
      'warehouses': '/api/warehouses',
      'users': '/api/users',
      'evaluations': '/api/evaluations',
      'maintenance-orders': '/api/maintenance-orders',
      'delivery-orders': '/api/delivery-orders',
      'internal-messages': '/api/internal-messages'
    };
    return mapping[dataType] || null;
  };

  // --- CRUD OPERATIONS ---

  // Device CRUD
  const addDevice = async (device: Device) => {
    try {
      const response = await apiClient.post('/api/devices', device);
      await handleApiResponse(response);

      // Invalidate devices cache
      invalidateCache('devices');

      // Update legacy state if needed
      const updatedDevices = await fetchDevicesData({ pagination: { limit: 1000 } });
      setDevices(updatedDevices.data);
    } catch (error) {
      console.error('خطأ في إضافة الجهاز:', error);
      throw error;
    }
  };

  const updateDevice = async (device: Device) => {
    try {
      const response = await apiClient.put('/api/devices', device);
      await handleApiResponse(response);

      // Invalidate devices cache
      invalidateCache('devices');

      // Update legacy state
      setDevices(prev => prev.map(d => d.id === device.id ? device : d));
    } catch (error) {
      console.error('خطأ في تحديث الجهاز:', error);
      throw error;
    }
  };

  const deleteDevice = async (deviceId: string) => {
    try {
      const response = await apiClient.delete('/api/devices', { id: deviceId });
      await handleApiResponse(response);

      // Invalidate devices cache
      invalidateCache('devices');

      // Update legacy state
      setDevices(prev => prev.filter(d => d.id !== deviceId));
    } catch (error) {
      console.error('خطأ في حذف الجهاز:', error);
      throw error;
    }
  };

  // Sale CRUD
  const addSale = async (sale: Sale) => {
    try {
      const response = await apiClient.post('/api/sales', sale);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('sales');
      invalidateCache('devices'); // Device statuses might change

      // Update legacy state
      const updatedSales = await fetchSalesData({ pagination: { limit: 1000 } });
      setSales(updatedSales.data);
    } catch (error) {
      console.error('خطأ في إضافة المبيعة:', error);
      throw error;
    }
  };

  const updateSale = async (sale: Sale) => {
    try {
      const response = await apiClient.put('/api/sales', sale);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('sales');
      invalidateCache('devices');

      // Update legacy state
      setSales(prev => prev.map(s => s.id === sale.id ? sale : s));
    } catch (error) {
      console.error('خطأ في تحديث المبيعة:', error);
      throw error;
    }
  };

  const deleteSale = async (saleId: number) => {
    try {
      const response = await apiClient.delete('/api/sales', { id: saleId });
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('sales');
      invalidateCache('devices');

      // Update legacy state
      setSales(prev => prev.filter(s => s.id !== saleId));
    } catch (error) {
      console.error('خطأ في حذف المبيعة:', error);
      throw error;
    }
  };

  // Supply Order CRUD
  const addSupplyOrder = async (order: SupplyOrder) => {
    try {
      const response = await apiClient.post('/api/supply', order);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('supply');
      invalidateCache('devices'); // New devices might be added

      // Update legacy state
      const updatedOrders = await fetchSupplyOrdersData({ pagination: { limit: 1000 } });
      setSupplyOrders(updatedOrders.data);
    } catch (error) {
      console.error('خطأ في إضافة أمر التوريد:', error);
      throw error;
    }
  };

  const updateSupplyOrder = async (order: SupplyOrder) => {
    try {
      const response = await apiClient.put('/api/supply', order);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('supply');
      invalidateCache('devices');

      // Update legacy state
      setSupplyOrders(prev => prev.map(o => o.id === order.id ? order : o));
    } catch (error) {
      console.error('خطأ في تحديث أمر التوريد:', error);
      throw error;
    }
  };

  const deleteSupplyOrder = async (orderId: number) => {
    try {
      const response = await apiClient.delete('/api/supply', { id: orderId });
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('supply');
      invalidateCache('devices');

      // Update legacy state
      setSupplyOrders(prev => prev.filter(o => o.id !== orderId));
    } catch (error) {
      console.error('خطأ في حذف أمر التوريد:', error);
      throw error;
    }
  };

  // Return CRUD
  const addReturn = async (returnOrder: Return) => {
    try {
      const response = await apiClient.post('/api/returns', returnOrder);
      await handleApiResponse(response);

      invalidateCache('returns');
      invalidateCache('devices');

      const updatedReturns = await fetchReturnsData({ pagination: { limit: 1000 } });
      setReturns(updatedReturns.data);
    } catch (error) {
      console.error('خطأ في إضافة المرتجع:', error);
      throw error;
    }
  };

  const updateReturn = async (returnOrder: Return) => {
    try {
      const response = await apiClient.put('/api/returns', returnOrder);
      await handleApiResponse(response);

      invalidateCache('returns');
      invalidateCache('devices');

      setReturns(prev => prev.map(r => r.id === returnOrder.id ? returnOrder : r));
    } catch (error) {
      console.error('خطأ في تحديث المرتجع:', error);
      throw error;
    }
  };

  const deleteReturn = async (returnId: number) => {
    try {
      const response = await apiClient.delete('/api/returns', { id: returnId });
      await handleApiResponse(response);

      invalidateCache('returns');
      invalidateCache('devices');

      setReturns(prev => prev.filter(r => r.id !== returnId));
    } catch (error) {
      console.error('خطأ في حذف المرتجع:', error);
      throw error;
    }
  };

  // Client CRUD
  const addClient = async (client: Contact) => {
    try {
      const response = await apiClient.post('/api/clients', client);
      await handleApiResponse(response);

      invalidateCache('clients');

      const updatedClients = await fetchClientsData({ pagination: { limit: 1000 } });
      setClients(updatedClients.data);
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  };

  const updateClient = async (client: Contact) => {
    try {
      const response = await apiClient.put('/api/clients', client);
      await handleApiResponse(response);

      invalidateCache('clients');

      setClients(prev => prev.map(c => c.id === client.id ? client : c));
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  };

  const deleteClient = async (clientId: number) => {
    try {
      const response = await apiClient.delete('/api/clients', { id: clientId });
      await handleApiResponse(response);

      invalidateCache('clients');

      setClients(prev => prev.filter(c => c.id !== clientId));
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  };

  // Supplier CRUD
  const addSupplier = async (supplier: Contact) => {
    try {
      const response = await apiClient.post('/api/suppliers', supplier);
      await handleApiResponse(response);

      invalidateCache('suppliers');

      const updatedSuppliers = await fetchSuppliersData({ pagination: { limit: 1000 } });
      setSuppliers(updatedSuppliers.data);
    } catch (error) {
      console.error('خطأ في إضافة المورد:', error);
      throw error;
    }
  };

  const updateSupplier = async (supplier: Contact) => {
    try {
      const response = await apiClient.put('/api/suppliers', supplier);
      await handleApiResponse(response);

      invalidateCache('suppliers');

      setSuppliers(prev => prev.map(s => s.id === supplier.id ? supplier : s));
    } catch (error) {
      console.error('خطأ في تحديث المورد:', error);
      throw error;
    }
  };

  const deleteSupplier = async (supplierId: number) => {
    try {
      const response = await apiClient.delete('/api/suppliers', { id: supplierId });
      await handleApiResponse(response);

      invalidateCache('suppliers');

      setSuppliers(prev => prev.filter(s => s.id !== supplierId));
    } catch (error) {
      console.error('خطأ في حذف المورد:', error);
      throw error;
    }
  };

  // Warehouse CRUD
  const addWarehouse = async (warehouse: Warehouse) => {
    try {
      const response = await apiClient.post('/api/warehouses', warehouse);
      await handleApiResponse(response);

      invalidateCache('warehouses');

      const updatedWarehouses = await fetchWarehousesData({ pagination: { limit: 1000 } });
      setWarehouses(updatedWarehouses.data);
    } catch (error) {
      console.error('خطأ في إضافة المخزن:', error);
      throw error;
    }
  };

  const updateWarehouse = async (warehouse: Warehouse) => {
    try {
      const response = await apiClient.put('/api/warehouses', warehouse);
      await handleApiResponse(response);

      invalidateCache('warehouses');

      setWarehouses(prev => prev.map(w => w.id === warehouse.id ? warehouse : w));
    } catch (error) {
      console.error('خطأ في تحديث المخزن:', error);
      throw error;
    }
  };

  const deleteWarehouse = async (warehouseId: number) => {
    try {
      const response = await apiClient.delete('/api/warehouses', { id: warehouseId });
      await handleApiResponse(response);

      invalidateCache('warehouses');

      setWarehouses(prev => prev.filter(w => w.id !== warehouseId));
    } catch (error) {
      console.error('خطأ في حذف المخزن:', error);
      throw error;
    }
  };

  // --- UTILITY FUNCTIONS ---

  const getDeviceById = (deviceId: string): Device | undefined => {
    return devices.find(d => d.id === deviceId);
  };

  const getWarehouseById = (warehouseId: number): Warehouse | undefined => {
    return warehouses.find(w => w.id === warehouseId);
  };

  const getClientById = (clientId: number): Contact | undefined => {
    return clients.find(c => c.id === clientId);
  };

  const getSupplierById = (supplierId: number): Contact | undefined => {
    return suppliers.find(s => s.id === supplierId);
  };

  const getUserById = (userId: number): User | undefined => {
    return users.find(u => u.id === userId);
  };

  // --- SYSTEM SETTINGS ---

  const updateSystemSettings = async (settings: Partial<SystemSettings>) => {
    try {
      const response = await apiClient.put('/api/settings', settings);
      const updatedSettings = await handleApiResponse(response);

      setSystemSettings(prev => ({ ...prev, ...updatedSettings }));
    } catch (error) {
      console.error('خطأ في تحديث إعدادات النظام:', error);
      throw error;
    }
  };

  // --- LEGACY COMPATIBILITY ---

  // Legacy function for backward compatibility
  const loadDataFromAPIs = async () => {
    try {
      setIsLoading(true);
      console.log("تحميل جميع البيانات (وضع التوافق القديم)...");

      // Load all data for legacy compatibility
      const [
        devicesData,
        salesData,
        supplyData,
        returnsData,
        clientsData,
        suppliersData,
        warehousesData,
        evaluationsData,
        maintenanceData,
        deliveryData,
        messagesData
      ] = await Promise.all([
        fetchDevicesData({ pagination: { limit: 1000 } }),
        fetchSalesData({ pagination: { limit: 1000 } }),
        fetchSupplyOrdersData({ pagination: { limit: 1000 } }),
        fetchReturnsData({ pagination: { limit: 1000 } }),
        fetchClientsData({ pagination: { limit: 1000 } }),
        fetchSuppliersData({ pagination: { limit: 1000 } }),
        fetchWarehousesData({ pagination: { limit: 1000 } }),
        fetchEvaluationOrdersData({ pagination: { limit: 1000 } }),
        fetchMaintenanceOrdersData({ pagination: { limit: 1000 } }),
        fetchDeliveryOrdersData({ pagination: { limit: 1000 } }),
        fetchInternalMessagesData({ pagination: { limit: 1000 } })
      ]);

      // Update legacy state
      setDevices(devicesData.data);
      setSales(salesData.data);
      setSupplyOrders(supplyData.data);
      setReturns(returnsData.data);
      setClients(clientsData.data);
      setSuppliers(suppliersData.data);
      setWarehouses(warehousesData.data);
      setEvaluationOrders(evaluationsData.data);
      setMaintenanceOrders(maintenanceData.data);
      setDeliveryOrders(deliveryData.data);
      setInternalMessages(messagesData.data);

      console.log("تم تحميل جميع البيانات بنجاح");
    } catch (error) {
      console.error("خطأ في تحميل البيانات:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // --- CONTEXT VALUE ---

  const contextValue: StoreContextType = {
    // Loading states
    isLoading,
    setIsLoading,

    // User and auth
    currentUser,
    setCurrentUser,
    users,

    // On-demand data fetching
    fetchDevicesData,
    fetchSalesData,
    fetchSupplyOrdersData,
    fetchReturnsData,
    fetchClientsData,
    fetchSuppliersData,
    fetchWarehousesData,
    fetchUsersData,
    fetchEvaluationOrdersData,
    fetchMaintenanceOrdersData,
    fetchDeliveryOrdersData,
    fetchInternalMessagesData,

    // Loading states
    getLoadingState,

    // Cache management
    invalidateCache,
    clearAllCache,
    getCacheStats,

    // Legacy data
    devices,
    sales,
    supplyOrders,
    returns,
    clients,
    suppliers,
    warehouses,
    evaluationOrders,
    maintenanceOrders,
    deliveryOrders,
    internalMessages,

    // CRUD operations
    addDevice,
    updateDevice,
    deleteDevice,
    addSale,
    updateSale,
    deleteSale,
    addSupplyOrder,
    updateSupplyOrder,
    deleteSupplyOrder,
    addReturn,
    updateReturn,
    deleteReturn,
    addClient,
    updateClient,
    deleteClient,
    addSupplier,
    updateSupplier,
    deleteSupplier,
    addWarehouse,
    updateWarehouse,
    deleteWarehouse,

    // Utility functions
    getDeviceById,
    getWarehouseById,
    getClientById,
    getSupplierById,
    getUserById,

    // System settings
    systemSettings,
    updateSystemSettings,

    // Legacy features
    maintenanceReceiptOrders,
    stocktakes,
    loadDataFromAPIs
  };

  return (
    <StoreContext.Provider value={contextValue}>
      {children}
    </StoreContext.Provider>
  );
};
