import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "15");

  try {
    const receipts = await prisma.maintenanceReceiptOrder.findMany({
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { createdAt: "desc" },
      include: {
        items: true,
        createdBy: {
          select: {
            name: true
          }
        },
        warehouse: {
          select: {
            name: true
          }
        }
      }
    });

    const total = await prisma.maintenanceReceiptOrder.count();

    return NextResponse.json({
      data: receipts,
      total,
      hasMore: receipts.length === limit,
      page
    });
  } catch (error) {
    console.error("Error fetching maintenance receipts:", error);
    return NextResponse.json(
      { error: "حدث خطأ أثناء جلب بيانات استلام الصيانة" },
      { status: 500 }
    );
  }
}
