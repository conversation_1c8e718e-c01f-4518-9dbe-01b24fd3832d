import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import {
  extractApiQueryParams,
  paginationToPrisma,
  sortToPrisma,
  searchToPrisma,
  filtersToPrisma,
  createPaginatedResponse,
  validatePaginationParams,
  validateSortParams
} from '@/lib/api-helpers';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استخراج معاملات API
    const allowedFilters = ['name'];
    const queryParams = extractApiQueryParams(request, allowedFilters);

    // التحقق من صحة المعاملات
    if (queryParams.pagination) {
      const paginationErrors = validatePaginationParams(queryParams.pagination);
      if (paginationErrors.length > 0) {
        return NextResponse.json({ error: paginationErrors.join(', ') }, { status: 400 });
      }
    }

    const allowedSortFields = ['id', 'name'];
    if (queryParams.sort) {
      const sortErrors = validateSortParams(queryParams.sort, allowedSortFields);
      if (sortErrors.length > 0) {
        return NextResponse.json({ error: sortErrors.join(', ') }, { status: 400 });
      }
    }

    // تحويل المعاملات إلى Prisma
    const paginationPrisma = paginationToPrisma(queryParams.pagination);
    const sortPrisma = sortToPrisma(queryParams.sort, allowedSortFields);
    const searchPrisma = searchToPrisma(queryParams.search, ['name']);
    const filtersPrisma = filtersToPrisma(queryParams.filters, allowedFilters);

    // دمج شروط البحث والتصفية
    const whereClause = {
      ...filtersPrisma,
      ...(searchPrisma && { ...searchPrisma })
    };

    // جلب العدد الإجمالي
    const total = await prisma.manufacturer.count({ where: whereClause });

    // جلب البيانات
    const manufacturers = await prisma.manufacturer.findMany({
      where: whereClause,
      ...paginationPrisma,
      orderBy: sortPrisma || { id: 'desc' }
    });

    // إنشاء الاستجابة المرقمة
    const response = createPaginatedResponse(manufacturers, total, queryParams.pagination || {}, queryParams);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Failed to fetch manufacturers:', error);
    return NextResponse.json({ error: 'Failed to fetch manufacturers' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newManufacturer = await request.json();

    // Basic validation
    if (!newManufacturer.name) {
      return NextResponse.json(
        { error: 'Manufacturer name is required' },
        { status: 400 }
      );
    }

    // Check if manufacturer already exists
    const existingManufacturer = await prisma.manufacturer.findFirst({
      where: {
        name: newManufacturer.name
      }
    });

    if (existingManufacturer) {
      return NextResponse.json(
        { error: 'Manufacturer with this name already exists' },
        { status: 400 }
      );
    }

    // Create the manufacturer
    const manufacturer = await prisma.manufacturer.create({
      data: {
        name: newManufacturer.name
      }
    });

    return NextResponse.json(manufacturer, { status: 201 });
  } catch (error) {
    console.error('Failed to create manufacturer:', error);
    return NextResponse.json({ error: 'Failed to create manufacturer' }, { status: 500 });
  }
}
