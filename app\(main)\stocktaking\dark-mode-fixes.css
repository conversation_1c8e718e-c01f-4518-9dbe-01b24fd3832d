/* تحسينات التباين والخلفيات في الوضع الليلي */

/* تحسين الخلفيات والألوان العامة */
.dark-mode {
  color-scheme: dark;
}

/* تحسين لون النص في القائمة المنسدلة */
.dark-mode select,
.dark-mode .select-trigger,
.dark-mode [data-radix-select-trigger],
.dark-mode .combobox-trigger,
.dark-mode input,
.dark-mode textarea,
.dark-mode [role="combobox"] {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(148, 163, 184, 0.4) !important;
  color: #f1f5f9 !important; /* تحسين التباين للنص */
  font-weight: 500 !important; /* تحسين وضوح النص */
}

/* تحسين محتوى القائمة المنسدلة */
.dark-mode .select-content,
.dark-mode [data-radix-select-content],
.dark-mode [role="listbox"],
.dark-mode select option,
.dark-mode .combobox-content,
.dark-mode .popover-content,
.dark-mode .dropdown-content {
  background: rgba(30, 41, 59, 0.98) !important;
  color: #f1f5f9 !important; /* تحسين التباين للنص */
  border: 1px solid rgba(148, 163, 184, 0.4) !important;
  font-weight: 500 !important; /* تحسين وضوح النص */
}

/* تحسين جداول الإدخال */
.dark-mode .data-table,
.dark-mode .input-table,
.dark-mode table {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(148, 163, 184, 0.4) !important;
  color: #f1f5f9 !important;
}

.dark-mode .data-table th,
.dark-mode .input-table th,
.dark-mode table th {
  background: rgba(51, 65, 85, 0.95) !important;
  color: #f1f5f9 !important;
  border-bottom: 2px solid rgba(148, 163, 184, 0.4) !important;
}

.dark-mode .data-table td,
.dark-mode .input-table td,
.dark-mode table td {
  background: rgba(30, 41, 59, 0.95) !important;
  color: #f1f5f9 !important;
  border: 1px solid rgba(148, 163, 184, 0.2) !important;
}

.dark-mode .data-table tr:hover td,
.dark-mode .input-table tr:hover td,
.dark-mode table tr:hover td {
  background: rgba(51, 65, 85, 0.95) !important;
}

/* تحسين العناصر عند التحويم والتحديد */
.dark-mode .select-item:hover,
.dark-mode [data-radix-select-item]:hover,
.dark-mode [data-radix-select-item][data-highlighted],
.dark-mode select option:hover,
.dark-mode select option:focus {
  background: rgba(59, 130, 246, 0.25) !important; /* زيادة التباين عند التحويم */
  color: #ffffff !important; /* تحسين وضوح النص عند التحويم */
}

/* تحسين حالة التركيز */
.dark-mode select:focus,
.dark-mode .select-trigger:focus,
.dark-mode [data-radix-select-trigger]:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4) !important; /* تحسين وضوح حالة التركيز */
  outline: none !important;
}

/* تحسين القيمة المحددة */
.dark-mode .select-value,
.dark-mode [data-radix-select-value] {
  color: #f1f5f9 !important; /* تحسين التباين للنص */
  font-weight: 500 !important; /* تحسين وضوح النص */
}

/* تحسين حالة التعطيل */
.dark-mode select:disabled,
.dark-mode .select-trigger:disabled,
.dark-mode [data-radix-select-trigger][data-disabled] {
  background: rgba(51, 65, 85, 0.5) !important;
  color: #94a3b8 !important;
  border-color: rgba(148, 163, 184, 0.2) !important;
  cursor: not-allowed;
}
