'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import type { Warehouse, WarehouseType } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Edit, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const initialFormState: Omit<Warehouse, 'id'> = {
  name: '',
  type: 'فرعي',
  location: '',
};

export default function WarehousesPage() {
  const { toast } = useToast();

  // State management
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(
    null
    );
  const [warehouseToDelete, setWarehouseToDelete] = useState<Warehouse | null>(
    null
    );
  const [formData, setFormData] =
    useState<Omit<Warehouse, 'id'>>(initialFormState);

  // API functions
  const fetchWarehouses = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/warehouses-simple');
      if (!response.ok) throw new Error('فشل في تحميل المخازن');
      const result = await response.json();

      // Handle both paginated and legacy response formats
      const data = result.data || result;
      setWarehouses(data);
    } catch (error) {
      console.error('خطأ في تحميل المخازن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل المخازن'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addWarehouse = async (warehouseData: any) => {
    try {
      const response = await fetch('/api/warehouses-simple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(warehouseData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'فشل في إنشاء المخزن');
      }

      await fetchWarehouses();
      return true;
    } catch (error: any) {
      console.error('خطأ في إنشاء المخزن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message || 'فشل في إنشاء المخزن'
      });
      return false;
    }
  };

  const updateWarehouse = async (warehouseData: any) => {
    try {
      const response = await fetch('/api/warehouses-simple', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(warehouseData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'فشل في تحديث المخزن');
      }

      await fetchWarehouses();
      return true;
    } catch (error: any) {
      console.error('خطأ في تحديث المخزن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message || 'فشل في تحديث المخزن'
      });
      return false;
    }
  };

  const deleteWarehouse = async (id: number) => {
    try {
      const response = await fetch(`/api/warehouses-simple?id=${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'فشل في حذف المخزن');
      }

      await fetchWarehouses();
      return true;
    } catch (error: any) {
      console.error('خطأ في حذف المخزن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message || 'فشل في حذف المخزن'
      });
      return false;
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchWarehouses();
  }, []);

  const handleOpenDialog = (warehouse: Warehouse | null) => {
    if (warehouse) {
      setEditingWarehouse(warehouse);
      setFormData(warehouse);
    } else {
      setEditingWarehouse(null);
      setFormData(initialFormState);
    }
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingWarehouse(null);
    setFormData(initialFormState);
  };

  const handleSave = async () => {
    if (!formData.name || !formData.location) {
      toast({
        title: 'خطأ في الإدخال',
        description: 'يرجى ملء جميع الحقول.',
        variant: 'destructive',
      });
      return;
    }

    let success = false;
    if (editingWarehouse) {
      success = await updateWarehouse({ ...formData, id: editingWarehouse.id });
      if (success) {
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث بيانات المخزن بنجاح.',
        });
      }
    } else {
      success = await addWarehouse(formData);
      if (success) {
        toast({ title: 'تمت الإضافة', description: 'تمت إضافة المخزن بنجاح.' });
      }
    }

    if (success) {
      handleDialogClose();
    }
  };

  const handleDelete = async () => {
    if (warehouseToDelete) {
      const success = await deleteWarehouse(warehouseToDelete.id);
      if (success) {
        toast({
          title: 'تم الحذف',
          description: `تم حذف مخزن ${warehouseToDelete.name} بنجاح.`,
        });
        setWarehouseToDelete(null);
      }
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>إدارة المخازن</CardTitle>
          <Button onClick={() => handleOpenDialog(null)}>
            إضافة مخزن جديد
          </Button>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>اسم المخزن</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>الموقع</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      جاري تحميل المخازن...
                    </TableCell>
                  </TableRow>
                ) : warehouses.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      لا توجد مخازن مسجلة
                    </TableCell>
                  </TableRow>
                ) : (
                  warehouses.map((warehouse) => (
                  <TableRow key={warehouse.id}>
                    <TableCell className="font-medium">
                      {warehouse.name}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          warehouse.type === 'رئيسي' ? 'default' : 'secondary'
                        }
                      >
                        {warehouse.type}
                      </Badge>
                    </TableCell>
                    <TableCell>{warehouse.location}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleOpenDialog(warehouse)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-destructive hover:text-destructive"
                          onClick={() => setWarehouseToDelete(warehouse)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-[425px]"
          onInteractOutside={(e) => e.preventDefault()}
          onCloseAutoFocus={handleDialogClose}
        >
          <DialogHeader>
            <DialogTitle>
              {editingWarehouse ? 'تعديل مخزن' : 'إضافة مخزن جديد'}
            </DialogTitle>
            <DialogDescription>
              {editingWarehouse
                ? 'قم بتحديث بيانات المخزن.'
                : 'أدخل بيانات المخزن الجديد هنا. انقر على "حفظ" عند الانتهاء.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                placeholder="مثال: مخزن الفرع الشمالي"
                className="col-span-3"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                النوع
              </Label>
              <Select
                dir="rtl"
                value={formData.type}
                onValueChange={(value: WarehouseType) =>
                  setFormData({ ...formData, type: value })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="رئيسي">رئيسي</SelectItem>
                  <SelectItem value="فرعي">فرعي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">
                الموقع
              </Label>
              <Input
                id="location"
                placeholder="مثال: صنعاء - شارع تعز"
                className="col-span-3"
                value={formData.location}
                onChange={(e) =>
                  setFormData({ ...formData, location: e.target.value })
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSave}>حفظ</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!warehouseToDelete}
        onOpenChange={() => setWarehouseToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">
              ⚠️ تأكيد حذف المخزن
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <div className="text-gray-700">
                  هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف المخزن
                  <span className="font-semibold text-red-600">
                    {' "' + warehouseToDelete?.name + '"'}
                  </span>
                  بشكل دائم.
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-3">
                  <div className="text-yellow-800 text-sm">
                    <strong>تنبيه:</strong> سيتم فحص العلاقات المرتبطة قبل الحذف.
                    إذا كان للمخزن مبيعات، أوامر توريد، تحويلات مخزنية، أوامر تسليم، أو أجهزة، فلن يتم الحذف.
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-100 hover:bg-gray-200">
              إلغاء
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
