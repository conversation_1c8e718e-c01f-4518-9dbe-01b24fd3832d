"use client";

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
  useCallback,
} from "react";
import {
  Device,
  Contact,
  Warehouse,
  Sale,
  SaleItem,
  Return,
  DeviceStatus,
  Manufacturer,
  DeviceModel,
  SupplyOrder,
  EvaluationOrder,
  MaintenanceLog,
  MaintenanceResult,
  WarehouseTransfer,
  User,
  SystemSettings,
  EmployeeRequest,
  EmployeeRequestStatus,
  InternalMessage,
  MessageStatus,
  AcceptanceOrder,
  MaintenanceOrder,
  DeliveryOrder,
  DeliveryOrderItem,
  MaintenanceReceiptOrder,
  DeviceReturnHistory,
  Stocktake,
  StocktakeDiscrepancy,
  StocktakeStatus,
  StocktakeFilter,
  StocktakeV1,
  StocktakeItemV1,
  AppPermissions,
  permissionPages,
  PaginatedResponse,
  ApiQueryParams,
  LoadingState,
  ActivityLog,
} from "@/lib/types";

// استيراد دوال جلب البيانات الجديدة
import {
  fetchDevices,
  fetchSales,
  fetchSupplyOrders,
  fetchReturns,
  fetchClients,
  fetchSuppliers,
  fetchWarehouses,
  fetchUsers,
  fetchEvaluationOrders,
  fetchMaintenanceOrders,
  fetchDeliveryOrders,
  fetchInternalMessages,
  dataFetcher
} from '@/lib/data-fetcher';

import { globalCache, staticDataCache, dynamicDataCache } from '@/lib/cache-manager';
import { apiClient, handleApiResponse } from "@/lib/api-client";

// --- STORE CONTEXT INTERFACE ---

interface StoreContextType {
  // --- LOADING STATES ---
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  
  // --- USER AND AUTH ---
  currentUser: User | null;
  setCurrentUser: (user: User | null) => void;
  users: User[];
  addUser: (user: Omit<User, 'id'>) => Promise<User>;
  updateUser: (user: User) => Promise<User>;
  deleteUser: (id: number) => Promise<void>;
  
    // --- ON-DEMAND DATA FETCHING ---
  fetchDevicesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Device>>;
  fetchSalesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Sale>>;
  fetchSupplyOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<SupplyOrder>>;
  fetchReturnsData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Return>>;
  fetchClientsData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Contact>>;
  fetchSuppliersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Contact>>;
  fetchWarehousesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<Warehouse>>;
  fetchUsersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<User>>;
  fetchEvaluationOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<EvaluationOrder>>;
  fetchMaintenanceOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<MaintenanceOrder>>;
  fetchDeliveryOrdersData: (params?: ApiQueryParams) => Promise<PaginatedResponse<DeliveryOrder>>;
  fetchInternalMessagesData: (params?: ApiQueryParams) => Promise<PaginatedResponse<InternalMessage>>;
  
  // دالة لتحميل البيانات الأساسية
  loadEssentialData: () => Promise<void>;
  
  // دالة لإعادة تحميل المخازن
  reloadWarehouses: () => Promise<Warehouse[]>;
  
  // دوال لإعادة تحميل العملاء والموردين
  reloadClients: () => Promise<Contact[]>;
  reloadSuppliers: () => Promise<Contact[]>;
  
  // --- LOADING STATES FOR EACH DATA TYPE ---
  getLoadingState: (dataType: string, params?: ApiQueryParams) => LoadingState;
  
  // --- CACHE MANAGEMENT ---
  invalidateCache: (dataType: string, params?: ApiQueryParams) => void;
  clearAllCache: () => void;
  getCacheStats: () => any;
  
  // --- LEGACY DATA (for backward compatibility) ---
  
  sales: Sale[];
  supplyOrders: SupplyOrder[];
  returns: Return[];
  clients: Contact[];
  suppliers: Contact[];
  warehouses: Warehouse[];
  evaluationOrders: EvaluationOrder[];
  maintenanceOrders: MaintenanceOrder[];
  deliveryOrders: DeliveryOrder[];
  internalMessages: InternalMessage[];
  activities: ActivityLog[];
  
  // --- CRUD OPERATIONS (updated to work with new system) ---
  addDevice: (device: Device) => Promise<void>;
  updateDevice: (device: Device) => Promise<void>;
  deleteDevice: (deviceId: string) => Promise<void>;
  
  addSale: (sale: Sale) => Promise<void>;
  updateSale: (sale: Sale) => Promise<void>;
  deleteSale: (saleId: number) => Promise<void>;
  
  addSupplyOrder: (order: SupplyOrder) => Promise<void>;
  updateSupplyOrder: (order: SupplyOrder) => Promise<void>;
  deleteSupplyOrder: (orderId: number) => Promise<void>;
  
  addReturn: (returnOrder: Return) => Promise<void>;
  updateReturn: (returnOrder: Return) => Promise<void>;
  deleteReturn: (returnId: number) => Promise<void>;
  
  addClient: (client: Contact) => Promise<void>;
  updateClient: (client: Contact) => Promise<void>;
  deleteClient: (clientId: number) => Promise<void>;
  
  addSupplier: (supplier: Contact) => Promise<void>;
  updateSupplier: (supplier: Contact) => Promise<void>;
  deleteSupplier: (supplierId: number) => Promise<void>;
  
  // دالة موحدة لإضافة جهة اتصال (عميل أو مورد)
  addContact: (type: 'client' | 'supplier', contactData: { name: string; phone: string; email: string }) => Promise<boolean>;
  
  addWarehouse: (warehouse: Warehouse) => Promise<void>;
  updateWarehouse: (warehouse: Warehouse) => Promise<void>;
  deleteWarehouse: (warehouseId: number) => Promise<void>;
  
  // --- UTILITY FUNCTIONS ---
  getDeviceById: (deviceId: string) => Device | undefined;
  getWarehouseById: (warehouseId: number) => Warehouse | undefined;
  getClientById: (clientId: number) => Contact | undefined;
  getSupplierById: (supplierId: number) => Contact | undefined;
  getUserById: (userId: number) => User | undefined;

  // --- ACTIVITY LOGGING ---
  addActivity: (log: Omit<ActivityLog, "id" | "date" | "username">) => void;
  
  // --- SYSTEM SETTINGS ---
  systemSettings: SystemSettings;
  updateSystemSettings: (settings: Partial<SystemSettings>) => Promise<void>;
  
  // --- MAINTENANCE AND OTHER LEGACY FEATURES ---
  maintenanceReceiptOrders: MaintenanceReceiptOrder[];
  stocktakes: StocktakeV1[];
  
  // Legacy functions that will be gradually migrated
  loadDataFromAPIs: () => Promise<void>;
}

// --- STORE CONTEXT ---

const StoreContext = createContext<StoreContextType | undefined>(undefined);

export const useStore = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error("useStore must be used within a StoreProvider");
  }
  return context;
};

// --- STORE PROVIDER ---

interface StoreProviderProps {
  children: ReactNode;
}

export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  // --- STATE MANAGEMENT ---
  const [isLoading, setIsLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isInitialized, setIsInitialized] = useState(false); // flag لمنع التحميل المتكرر
  
  // Legacy state for backward compatibility
  const [users, setUsers] = useState<User[]>([]);
  const [devices, setDevices] = useState<Device[]>([]);
  
  const [sales, setSales] = useState<Sale[]>([]);
  const [supplyOrders, setSupplyOrders] = useState<SupplyOrder[]>([]);
  const [returns, setReturns] = useState<Return[]>([]);
  const [clients, setClients] = useState<Contact[]>([]);
  const [suppliers, setSuppliers] = useState<Contact[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([]);
  const [evaluationOrders, setEvaluationOrders] = useState<EvaluationOrder[]>([]);
  const [maintenanceOrders, setMaintenanceOrders] = useState<MaintenanceOrder[]>([]);
  const [deliveryOrders, setDeliveryOrders] = useState<DeliveryOrder[]>([]);
  const [internalMessages, setInternalMessages] = useState<InternalMessage[]>([]);
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState<MaintenanceReceiptOrder[]>([]);
  const [stocktakes, setStocktakes] = useState<StocktakeV1[]>([]);
  
  // System settings
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    logoUrl: "/logo.png",
    companyNameAr: "شركة الأجهزة الذكية",
    companyNameEn: "Smart Devices Company",
    addressAr: "الرياض، المملكة العربية السعودية",
    addressEn: "Riyadh, Saudi Arabia",
    phone: "+966 11 123 4567",
    email: "<EMAIL>",
    website: "www.smartdevices.sa",
    footerTextAr: "جميع الحقوق محفوظة © 2024",
    footerTextEn: "All Rights Reserved © 2024",
    reportLayout: {
      logo: {
        size: 60,
        position: 'right',
        marginTop: 10,
        marginBottom: 15,
        marginLeft: 15,
        marginRight: 15,
      },
      title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#1a1a1a',
        position: 'center',
        marginTop: 10,
        marginBottom: 15,
      },
      companyInfo: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
        position: 'right',
        marginTop: 5,
        marginBottom: 20,
        showArabic: true,
        showEnglish: false,
      },
      table: {
        headerBackgroundColor: '#2c3e50',
        headerTextColor: '#ffffff',
        rowAlternateColor: '#f8f9fa',
        borderColor: '#dee2e6',
        fontSize: 11,
        cellPadding: 8,
      },
      footer: {
        fontSize: 10,
        color: '#666666',
        position: 'center',
        marginTop: 20,
        showPageNumbers: true,
        showDate: true,
      },
      page: {
        marginTop: 20,
        marginBottom: 20,
        marginLeft: 20,
        marginRight: 20,
        orientation: 'portrait',
      },
    }
  });

  // --- INITIALIZATION ---
  useEffect(() => {
    if (typeof window !== "undefined" && !isInitialized) {
      // Load essential data only (users for authentication)
      loadEssentialData();
    }
  }, [isInitialized]); // إضافة isInitialized كـ dependency

  // إزالة useEffect المتكرر الذي يسبب loop لا نهائي
  // useEffect(() => {
  //   if (currentUser && warehouses.length > 0 && clients.length === 0 && suppliers.length === 0) {
  //     // إعادة تحميل العملاء والموردين إذا لم يكونوا محملين
  //     reloadClients();
  //     reloadSuppliers();
  //   }
  // }, [currentUser, warehouses]);

  // Load only essential data for app initialization
  const loadEssentialData = useCallback(async () => {
    if (isInitialized) {
      console.log("البيانات محملة بالفعل، تجاهل التحميل المتكرر");
      return;
    }
    
    try {
      setIsLoading(true);
      console.log("تحميل البيانات الأساسية...");

      // إنشاء مستخدم افتراضي بصلاحيات كاملة للاختبار
      const defaultAdmin: User = {
        id: 1,
        name: 'المدير العام',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        permissions: permissionPages.reduce(
          (acc, page) => ({
            ...acc,
            [page]: { 
              view: true, 
              create: true, 
              edit: true, 
              delete: true,
              viewAll: true,
              manage: [],
              acceptWithoutWarranty: true
            },
          }),
          {} as AppPermissions
        ),
        status: 'Active',
        lastLogin: new Date().toISOString()
      };

      // تحميل أو إنشاء قائمة المستخدمين
      try {
        const usersData = await fetchUsersData({ pagination: { limit: 100 } });
        const users = usersData.data as User[];
        
        // إذا لم يكن هناك مستخدمين، أضف المستخدم الافتراضي
        if (users.length === 0) {
          setUsers([defaultAdmin]);
        } else {
          setUsers(users);
        }
      } catch (error) {
        console.log("لا توجد قاعدة بيانات مستخدمين، سيتم إنشاء مستخدم افتراضي");
        setUsers([defaultAdmin]);
      }

      // تحميل المخازن الأساسية (مهم لجميع الأقسام)
      try {
        console.log("تحميل المخازن...");
        const devToken = btoa('user:admin:admin');
        
        const warehousesResponse = await fetch('/api/warehouses-simple?limit=100', {
          headers: {
            'Authorization': `Bearer ${devToken}`
          }
        });
        
        if (warehousesResponse.ok) {
          const warehousesData = await warehousesResponse.json();
          const warehousesList = warehousesData.data as Warehouse[];
          setWarehouses(warehousesList);
          console.log(`تم تحميل ${warehousesList.length} مخزن`);
        } else {
          console.error("خطأ في تحميل المخازن:", warehousesResponse.status);
          // إنشاء مخزن افتراضي في حالة الخطأ
          const defaultWarehouse: Warehouse = {
            id: 1,
            name: 'المخزن الرئيسي',
            type: 'رئيسي',
            location: 'المقر الرئيسي'
          };
          setWarehouses([defaultWarehouse]);
        }
      } catch (error) {
        console.error("خطأ في تحميل المخازن:", error);
        // إنشاء مخزن افتراضي في حالة الخطأ
        const defaultWarehouse: Warehouse = {
          id: 1,
          name: 'المخزن الرئيسي',
          type: 'رئيسي',
          location: 'المقر الرئيسي'
        };
        setWarehouses([defaultWarehouse]);
        console.log("تم إنشاء مخزن افتراضي");
      }

      // تحميل العملاء والموردين الأساسيين (مهم لجميع الأقسام)
      try {
        console.log("تحميل العملاء والموردين...");
        
        const [clientsResponse, suppliersResponse] = await Promise.all([
          fetch('/api/clients-simple?limit=100'),
          fetch('/api/suppliers-simple?limit=100')
        ]);
        
        if (clientsResponse.ok) {
          const clientsList = await clientsResponse.json() as Contact[];
          setClients(clientsList);
          console.log(`تم تحميل ${clientsList.length} عميل`);
        } else {
          console.error("خطأ في تحميل العملاء:", clientsResponse.status);
          setClients([]);
        }
        
        if (suppliersResponse.ok) {
          const suppliersList = await suppliersResponse.json() as Contact[];
          setSuppliers(suppliersList);
          console.log(`تم تحميل ${suppliersList.length} مورد`);
        } else {
          console.error("خطأ في تحميل الموردين:", suppliersResponse.status);
          setSuppliers([]);
        }
      } catch (error) {
        console.error("خطأ في تحميل العملاء والموردين:", error);
        setClients([]);
        setSuppliers([]);
      }

      // تعيين المستخدم الحالي
      if (!currentUser) {
        setCurrentUser(defaultAdmin);
        console.log("تم تسجيل الدخول تلقائياً كمدير:", defaultAdmin.name);
      }

      console.log("تم تحميل البيانات الأساسية بنجاح");
    } catch (error) {
      console.error("خطأ في تحميل البيانات الأساسية:", error);
      
      // في حالة الخطأ، أنشئ مستخدم افتراضي
      const fallbackAdmin: User = {
        id: 1,
        name: 'المدير العام',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        permissions: permissionPages.reduce(
          (acc, page) => ({
            ...acc,
            [page]: { 
              view: true, 
              create: true, 
              edit: true, 
              delete: true,
              viewAll: true,
              manage: [],
              acceptWithoutWarranty: true
            },
          }),
          {} as AppPermissions
        ),
        status: 'Active',
        lastLogin: new Date().toISOString()
      };
      
      setUsers([fallbackAdmin]);
      setCurrentUser(fallbackAdmin);
      
      // إنشاء بيانات أساسية في حالة الخطأ
      const defaultWarehouse: Warehouse = {
        id: 1,
        name: 'المخزن الرئيسي',
        type: 'رئيسي',
        location: 'المقر الرئيسي'
      };
      setWarehouses([defaultWarehouse]);
      setClients([]);
      setSuppliers([]);
      
      console.log("تم إنشاء مستخدم افتراضي والبيانات الأساسية للاستخدام الطارئ");
    } finally {
      setIsLoading(false);
      setIsInitialized(true); // تحديد أن التحميل تم
    }
  }, [isInitialized]); // dependency على isInitialized

  // --- ON-DEMAND DATA FETCHING FUNCTIONS ---
  
  const fetchDevicesData = async (params: ApiQueryParams = {}) => {
    return await fetchDevices(params);
  };

  const fetchSalesData = async (params: ApiQueryParams = {}) => {
    return await fetchSales(params);
  };

  const fetchSupplyOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchSupplyOrders(params);
  };

  const fetchReturnsData = async (params: ApiQueryParams = {}) => {
    return await fetchReturns(params);
  };

  const fetchClientsData = async (params: ApiQueryParams = {}) => {
    return await fetchClients(params);
  };

  const fetchSuppliersData = async (params: ApiQueryParams = {}) => {
    return await fetchSuppliers(params);
  };

  const fetchWarehousesData = async (params: ApiQueryParams = {}) => {
    return await fetchWarehouses(params);
  };

  const fetchUsersData = async (params: ApiQueryParams = {}) => {
    const result = await fetchUsers(params);
    // تحديث البيانات المحلية أيضاً
    setUsers(result.data as User[]);
    return result;
  };

  const fetchEvaluationOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchEvaluationOrders(params);
  };

  const fetchMaintenanceOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchMaintenanceOrders(params);
  };

  const fetchDeliveryOrdersData = async (params: ApiQueryParams = {}) => {
    return await fetchDeliveryOrders(params);
  };

  const fetchInternalMessagesData = async (params: ApiQueryParams = {}) => {
    return await fetchInternalMessages(params);
  };

  // دالة لإعادة تحميل المخازن (لتحديث جميع الصفحات)
  const reloadWarehouses = async () => {
    try {
      console.log("إعادة تحميل المخازن...");
      const devToken = btoa('user:admin:admin');
      
      const response = await fetch('/api/warehouses-simple?limit=1000', {
        headers: {
          'Authorization': `Bearer ${devToken}`
        }
      });
      
      if (response.ok) {
        const warehousesData = await response.json();
        const warehousesList = warehousesData.data as Warehouse[];
        setWarehouses(warehousesList);
        console.log(`تم إعادة تحميل ${warehousesList.length} مخزن`);
        return warehousesList;
      } else {
        console.error("خطأ في إعادة تحميل المخازن:", response.status);
        return [];
      }
    } catch (error) {
      console.error("خطأ في إعادة تحميل المخازن:", error);
      return [];
    }
  };

  // دالة لإعادة تحميل العملاء
  const reloadClients = useCallback(async () => {
    try {
      console.log("إعادة تحميل العملاء...");
      
      const response = await fetch('/api/clients-simple?limit=1000');
      
      if (response.ok) {
        const clientsList = await response.json() as Contact[];
        setClients(clientsList);
        console.log(`تم إعادة تحميل ${clientsList.length} عميل`);
        return clientsList;
      } else {
        console.error("خطأ في تحميل العملاء:", response.status);
        return [];
      }
    } catch (error) {
      console.error("خطأ في إعادة تحميل العملاء:", error);
      return [];
    }
  }, []); // useCallback مع dependency array فارغ

  // دالة لإعادة تحميل الموردين
  const reloadSuppliers = useCallback(async () => {
    try {
      console.log("إعادة تحميل الموردين...");
      
      const response = await fetch('/api/suppliers-simple?limit=1000');
      
      if (response.ok) {
        const suppliersList = await response.json() as Contact[];
        setSuppliers(suppliersList);
        console.log(`تم إعادة تحميل ${suppliersList.length} مورد`);
        return suppliersList;
      } else {
        console.error("خطأ في تحميل الموردين:", response.status);
        return [];
      }
    } catch (error) {
      console.error("خطأ في إعادة تحميل الموردين:", error);
      return [];
    }
  }, []); // useCallback مع dependency array فارغ

  // --- LOADING STATE MANAGEMENT ---
  
  const getLoadingState = (dataType: string, params: ApiQueryParams = {}): LoadingState => {
    const cacheKey = `${dataType}|${JSON.stringify(params)}`;
    return dataFetcher.getLoadingState(cacheKey);
  };

  // --- CACHE MANAGEMENT ---
  
  const invalidateCache = (dataType: string, params: ApiQueryParams = {}) => {
    const endpoint = getEndpointForDataType(dataType);
    if (endpoint) {
      dataFetcher.invalidateCache(endpoint, params);
    }
  };

  const clearAllCache = () => {
    dataFetcher.clearAllCache();
  };

  const getCacheStats = () => {
    return {
      global: globalCache.getStats(),
      static: staticDataCache.getStats(),
      dynamic: dynamicDataCache.getStats()
    };
  };

  // Helper function to map data types to endpoints
  const getEndpointForDataType = (dataType: string): string | null => {
    const mapping: Record<string, string> = {
      'devices': '/api/devices',
      'sales': '/api/sales',
      'supply': '/api/supply',
      'returns': '/api/returns',
      'clients': '/api/clients',
      'suppliers': '/api/suppliers',
      'warehouses': '/api/warehouses',
      'users': '/api/users',
      'evaluations': '/api/evaluations',
      'maintenance-orders': '/api/maintenance-orders',
      'delivery-orders': '/api/delivery-orders',
      'internal-messages': '/api/internal-messages'
    };
    return mapping[dataType] || null;
  };

  // --- CRUD OPERATIONS ---

  // Device CRUD
  const addDevice = async (device: Device) => {
    try {
      const response = await apiClient.post('/api/devices', device);
      await handleApiResponse(response);

      // Invalidate devices cache
      invalidateCache('devices');

      // Update legacy state if needed
      const updatedDevices = await fetchDevicesData({ pagination: { limit: 1000 } });
      setDevices(updatedDevices.data as Device[]);
    } catch (error) {
      console.error('خطأ في إضافة الجهاز:', error);
      throw error;
    }
  };

  const updateDevice = async (device: Device) => {
    try {
      const response = await apiClient.put('/api/devices', device);
      await handleApiResponse(response);

      // Invalidate devices cache
      invalidateCache('devices');

      // Update legacy state
      setDevices(prev => prev.map(d => d.id === device.id ? device : d));
    } catch (error) {
      console.error('خطأ في تحديث الجهاز:', error);
      throw error;
    }
  };

  const deleteDevice = async (deviceId: string) => {
    try {
      const response = await apiClient.delete('/api/devices', { id: deviceId });
      await handleApiResponse(response);

      // Invalidate devices cache
      invalidateCache('devices');

      // Update legacy state
      setDevices(prev => prev.filter(d => d.id !== deviceId));
    } catch (error) {
      console.error('خطأ في حذف الجهاز:', error);
      throw error;
    }
  };

  // Sale CRUD
  const addSale = async (sale: Sale) => {
    try {
      const response = await apiClient.post('/api/sales', sale);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('sales');
      invalidateCache('devices'); // Device statuses might change

      // Update legacy state
      const updatedSales = await fetchSalesData({ pagination: { limit: 1000 } });
      setSales(updatedSales.data as Sale[]);
    } catch (error) {
      console.error('خطأ في إضافة المبيعة:', error);
      throw error;
    }
  };

  const updateSale = async (sale: Sale) => {
    try {
      const response = await apiClient.put('/api/sales', sale);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('sales');
      invalidateCache('devices');

      // Update legacy state
      setSales(prev => prev.map(s => s.id === sale.id ? sale : s));
    } catch (error) {
      console.error('خطأ في تحديث المبيعة:', error);
      throw error;
    }
  };

  const deleteSale = async (saleId: number) => {
    try {
      const response = await apiClient.delete('/api/sales', { id: saleId });
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('sales');
      invalidateCache('devices');

      // Update legacy state
      setSales(prev => prev.filter(s => s.id !== saleId));
    } catch (error) {
      console.error('خطأ في حذف المبيعة:', error);
      throw error;
    }
  };

  // Supply Order CRUD
  const addSupplyOrder = async (order: SupplyOrder) => {
    try {
      const response = await apiClient.post('/api/supply', order);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('supply');
      invalidateCache('devices'); // New devices might be added

      // Update legacy state
      const updatedOrders = await fetchSupplyOrdersData({ pagination: { limit: 1000 } });
      setSupplyOrders(updatedOrders.data as SupplyOrder[]);
    } catch (error) {
      console.error('خطأ في إضافة أمر التوريد:', error);
      throw error;
    }
  };

  const updateSupplyOrder = async (order: SupplyOrder) => {
    try {
      const response = await apiClient.put('/api/supply', order);
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('supply');
      invalidateCache('devices');

      // Update legacy state
      setSupplyOrders(prev => prev.map(o => o.id === order.id ? order : o));
    } catch (error) {
      console.error('خطأ في تحديث أمر التوريد:', error);
      throw error;
    }
  };

  const deleteSupplyOrder = async (orderId: number) => {
    try {
      const response = await apiClient.delete('/api/supply', { id: orderId });
      await handleApiResponse(response);

      // Invalidate related caches
      invalidateCache('supply');
      invalidateCache('devices');

      // Update legacy state
      setSupplyOrders(prev => prev.filter(o => o.id !== orderId));
    } catch (error) {
      console.error('خطأ في حذف أمر التوريد:', error);
      throw error;
    }
  };

  // Return CRUD
  const addReturn = async (returnOrder: Return) => {
    try {
      const response = await apiClient.post('/api/returns', returnOrder);
      await handleApiResponse(response);

      invalidateCache('returns');
      invalidateCache('devices');

      const updatedReturns = await fetchReturnsData({ pagination: { limit: 1000 } });
      setReturns(updatedReturns.data as Return[]);
    } catch (error) {
      console.error('خطأ في إضافة المرتجع:', error);
      throw error;
    }
  };

  const updateReturn = async (returnOrder: Return) => {
    try {
      const response = await apiClient.put('/api/returns', returnOrder);
      await handleApiResponse(response);

      invalidateCache('returns');
      invalidateCache('devices');

      setReturns(prev => prev.map(r => r.id === returnOrder.id ? returnOrder : r));
    } catch (error) {
      console.error('خطأ في تحديث المرتجع:', error);
      throw error;
    }
  };

  const deleteReturn = async (returnId: number) => {
    try {
      const response = await apiClient.delete('/api/returns', { id: returnId });
      await handleApiResponse(response);

      invalidateCache('returns');
      invalidateCache('devices');

      setReturns(prev => prev.filter(r => r.id !== returnId));
    } catch (error) {
      console.error('خطأ في حذف المرتجع:', error);
      throw error;
    }
  };

  // Client CRUD
  const addClient = async (client: Contact) => {
    try {
      const response = await apiClient.post('/api/clients', client);
      const newClient = await handleApiResponse(response);

      invalidateCache('clients');

      // تحديث البيانات المحلية فوراً
      setClients(prev => [...prev, newClient as Contact]);
      
      console.log("تم إضافة العميل وتحديث القائمة");
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  };

  const updateClient = async (client: Contact) => {
    try {
      const response = await apiClient.put('/api/clients', client);
      await handleApiResponse(response);

      invalidateCache('clients');

      setClients(prev => prev.map(c => c.id === client.id ? client : c));
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  };

  const deleteClient = async (clientId: number) => {
    try {
      const response = await apiClient.delete('/api/clients', { id: clientId });
      await handleApiResponse(response);

      invalidateCache('clients');

      setClients(prev => prev.filter(c => c.id !== clientId));
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  };

  // Supplier CRUD
  const addSupplier = async (supplier: Contact) => {
    try {
      const response = await apiClient.post('/api/suppliers', supplier);
      const newSupplier = await handleApiResponse(response);

      invalidateCache('suppliers');

      // تحديث البيانات المحلية فوراً
      setSuppliers(prev => [...prev, newSupplier as Contact]);
      
      console.log("تم إضافة المورد وتحديث القائمة");
    } catch (error) {
      console.error('خطأ في إضافة المورد:', error);
      throw error;
    }
  };

  const updateSupplier = async (supplier: Contact) => {
    try {
      const response = await apiClient.put('/api/suppliers', supplier);
      await handleApiResponse(response);

      invalidateCache('suppliers');

      setSuppliers(prev => prev.map(s => s.id === supplier.id ? supplier : s));
    } catch (error) {
      console.error('خطأ في تحديث المورد:', error);
      throw error;
    }
  };

  const deleteSupplier = async (supplierId: number) => {
    try {
      const response = await apiClient.delete('/api/suppliers', { id: supplierId });
      await handleApiResponse(response);

      invalidateCache('suppliers');

      setSuppliers(prev => prev.filter(s => s.id !== supplierId));
    } catch (error) {
      console.error('خطأ في حذف المورد:', error);
      throw error;
    }
  };

  // دالة موحدة لإضافة جهة اتصال (عميل أو مورد)
  const addContact = useCallback(async (type: 'client' | 'supplier', contactData: { name: string; phone: string; email: string }): Promise<boolean> => {
    try {
      console.log(`إضافة ${type === 'client' ? 'عميل' : 'مورد'} جديد:`, contactData);
      
      const endpoint = type === 'client' ? '/api/clients-simple' : '/api/suppliers-simple';
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: contactData.name,
          phone: contactData.phone || '',
          email: contactData.email || ''
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('خطأ من الخادم:', errorData);
        throw new Error(errorData.error || `Failed to create ${type}`);
      }

      const newContact = await response.json();
      console.log(`تم إنشاء ${type === 'client' ? 'العميل' : 'المورد'} بنجاح:`, newContact);

      // تحديث البيانات المحلية فوراً
      if (type === 'client') {
        setClients(prev => [...prev, newContact]);
        invalidateCache('clients');
        console.log('تم إضافة العميل إلى القائمة المحلية');
      } else {
        setSuppliers(prev => [...prev, newContact]);
        invalidateCache('suppliers');
        console.log('تم إضافة المورد إلى القائمة المحلية');
      }
      
      return true; // نجح الإنشاء
    } catch (error) {
      console.error(`خطأ في إضافة ${type === 'client' ? 'العميل' : 'المورد'}:`, error);
      return false; // فشل الإنشاء
    }
  }, []); // useCallback مع dependency array فارغ

  // --- USER MANAGEMENT FUNCTIONS ---
  const addUser = useCallback(async (userData: Omit<User, 'id'>): Promise<User> => {
    try {
      console.log('إضافة مستخدم جديد:', userData);

      // إرسال البيانات إلى قاعدة البيانات
      const response = await apiClient.post('/api/users', userData);
      const newUser = await handleApiResponse(response);

      invalidateCache('users');

      // تحديث البيانات المحلية فوراً
      setUsers(prev => [...prev, newUser as User]);

      console.log('تم إضافة المستخدم بنجاح:', newUser);
      return newUser as User;
    } catch (error) {
      console.error('خطأ في إضافة المستخدم:', error);
      throw error;
    }
  }, []);

  const updateUser = useCallback(async (user: User): Promise<User> => {
    try {
      console.log('تحديث المستخدم:', user);

      // إرسال التحديث إلى قاعدة البيانات
      const response = await apiClient.put('/api/users', user);
      const updatedUser = await handleApiResponse(response);

      invalidateCache('users');

      // تحديث البيانات المحلية
      setUsers(prev => prev.map(u => u.id === user.id ? updatedUser as User : u));

      // إذا كان المستخدم المحدث هو المستخدم الحالي، حدث currentUser أيضاً
      if (currentUser && currentUser.id === user.id) {
        setCurrentUser(updatedUser as User);
      }

      console.log('تم تحديث المستخدم بنجاح:', updatedUser);
      return updatedUser as User;
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error);
      throw error;
    }
  }, [currentUser]);

  const deleteUser = useCallback(async (id: number): Promise<void> => {
    try {
      console.log('حذف المستخدم بالرقم:', id);

      // إرسال طلب الحذف إلى قاعدة البيانات
      const response = await apiClient.delete('/api/users', { id });
      await handleApiResponse(response);

      invalidateCache('users');

      // تحديث البيانات المحلية
      setUsers(prev => prev.filter(u => u.id !== id));

      // إذا كان المستخدم المحذوف هو المستخدم الحالي، اجعل currentUser null
      if (currentUser && currentUser.id === id) {
        setCurrentUser(null);
      }

      console.log('تم حذف المستخدم بنجاح');
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error);
      throw error;
    }
  }, [currentUser]);

  // Warehouse CRUD
  const addWarehouse = async (warehouse: Warehouse) => {
    try {
      const response = await apiClient.post('/api/warehouses', warehouse);
      await handleApiResponse(response);

      invalidateCache('warehouses');

      // تحديث البيانات المحلية فوراً
      const updatedWarehouses = await fetchWarehousesData({ pagination: { limit: 1000 } });
      setWarehouses(updatedWarehouses.data as Warehouse[]);
      
      console.log("تم إضافة المخزن وتحديث القائمة");
    } catch (error) {
      console.error('خطأ في إضافة المخزن:', error);
      throw error;
    }
  };

  const updateWarehouse = async (warehouse: Warehouse) => {
    try {
      const response = await apiClient.put('/api/warehouses', warehouse);
      await handleApiResponse(response);

      invalidateCache('warehouses');

      // تحديث البيانات المحلية فوراً
      setWarehouses(prev => prev.map(w => w.id === warehouse.id ? warehouse : w));
      
      console.log("تم تحديث المخزن");
    } catch (error) {
      console.error('خطأ في تحديث المخزن:', error);
      throw error;
    }
  };

  const deleteWarehouse = async (warehouseId: number) => {
    try {
      const response = await apiClient.delete('/api/warehouses', { id: warehouseId });
      await handleApiResponse(response);

      invalidateCache('warehouses');

      // تحديث البيانات المحلية فوراً
      setWarehouses(prev => prev.filter(w => w.id !== warehouseId));
      
      console.log("تم حذف المخزن");
    } catch (error) {
      console.error('خطأ في حذف المخزن:', error);
      throw error;
    }
  };

  // --- UTILITY FUNCTIONS ---

  const getDeviceById = (deviceId: string): Device | undefined => {
    return devices.find(d => d.id === deviceId);
  };

  const getWarehouseById = (warehouseId: number): Warehouse | undefined => {
    return warehouses.find(w => w.id === warehouseId);
  };

  const getClientById = (clientId: number): Contact | undefined => {
    return clients.find(c => c.id === clientId);
  };

  const getSupplierById = (supplierId: number): Contact | undefined => {
    return suppliers.find(s => s.id === supplierId);
  };

  const getUserById = (userId: number): User | undefined => {
    return users.find(u => u.id === userId);
  };

  // --- ACTIVITY LOGGING ---

  const addActivity = (log: Omit<ActivityLog, "id" | "date" | "username">) => {
    if (!currentUser) return;
    const newActivity: ActivityLog = {
      ...log,
      id: `act-${Date.now()}`,
      date: new Date(),
      username: currentUser.name,
    };
    setActivities((prev) => [newActivity, ...prev]);
  };

  // --- SYSTEM SETTINGS ---

  const updateSystemSettings = async (settings: Partial<SystemSettings>) => {
    try {
      const response = await apiClient.put('/api/settings', settings);
      const updatedSettings = await handleApiResponse(response);

      setSystemSettings(prev => ({ ...prev, ...(updatedSettings as Partial<SystemSettings>) }));
    } catch (error) {
      console.error('خطأ في تحديث إعدادات النظام:', error);
      throw error;
    }
  };

  // --- LEGACY COMPATIBILITY ---

  // Legacy function for backward compatibility - NOW DISABLED BY DEFAULT
  const loadDataFromAPIs = async () => {
    console.warn("تم استدعاء loadDataFromAPIs - هذه الوظيفة قديمة ولا يُنصح باستخدامها");
    console.warn("استخدم fetchXXXData بدلاً من ذلك لتحميل البيانات عند الحاجة");
    
    // لا نحمل البيانات تلقائياً لتجنب مشاكل الأداء
    // إذا كنت تحتاج البيانات القديمة، قم بإزالة هذا التعليق:
    /*
    try {
      setIsLoading(true);
      console.log("تحميل جميع البيانات (وضع التوافق القديم)...");

      // Load all data for legacy compatibility
      const [
        devicesData,
        salesData,
        supplyData,
        returnsData,
        clientsData,
        suppliersData,
        warehousesData,
        evaluationsData,
        maintenanceData,
        deliveryData,
        messagesData
      ] = await Promise.all([
        fetchDevicesData({ pagination: { limit: 1000 } }),
        fetchSalesData({ pagination: { limit: 1000 } }),
        fetchSupplyOrdersData({ pagination: { limit: 1000 } }),
        fetchReturnsData({ pagination: { limit: 1000 } }),
        fetchClientsData({ pagination: { limit: 1000 } }),
        fetchSuppliersData({ pagination: { limit: 1000 } }),
        fetchWarehousesData({ pagination: { limit: 1000 } }),
        fetchEvaluationOrdersData({ pagination: { limit: 1000 } }),
        fetchMaintenanceOrdersData({ pagination: { limit: 1000 } }),
        fetchDeliveryOrdersData({ pagination: { limit: 1000 } }),
        fetchInternalMessagesData({ pagination: { limit: 1000 } })
      ]);

      // Update legacy state with proper type casting
      setDevices(devicesData.data as Device[]);
      setSales(salesData.data as Sale[]);
      setSupplyOrders(supplyData.data as SupplyOrder[]);
      setReturns(returnsData.data as Return[]);
      setClients(clientsData.data as Contact[]);
      setSuppliers(suppliersData.data as Contact[]);
      setWarehouses(warehousesData.data as Warehouse[]);
      setEvaluationOrders(evaluationsData.data as EvaluationOrder[]);
      setMaintenanceOrders(maintenanceData.data as MaintenanceOrder[]);
      setDeliveryOrders(deliveryData.data as DeliveryOrder[]);
      setInternalMessages(messagesData.data as InternalMessage[]);

      console.log("تم تحميل جميع البيانات بنجاح");
    } catch (error) {
      console.error("خطأ في تحميل البيانات:", error);
    } finally {
      setIsLoading(false);
    }
    */
  };

  // --- CONTEXT VALUE ---

  const contextValue: StoreContextType = {
    // Loading states
    isLoading,
    setIsLoading,

    // User and auth
    currentUser,
    setCurrentUser,
    users,

    // On-demand data fetching
    fetchDevicesData,
    fetchSalesData,
    fetchSupplyOrdersData,
    fetchReturnsData,
    fetchClientsData,
    fetchSuppliersData,
    fetchWarehousesData,
    fetchUsersData,
    fetchEvaluationOrdersData,
    fetchMaintenanceOrdersData,
    fetchDeliveryOrdersData,
    fetchInternalMessagesData,
    loadEssentialData,
    reloadWarehouses,
    reloadClients,
    reloadSuppliers,

    // Loading states
    getLoadingState,

    // Cache management
    invalidateCache,
    clearAllCache,
    getCacheStats,

    // Legacy data
    devices,
    sales,
    supplyOrders,
    returns,
    clients,
    suppliers,
    warehouses,
    evaluationOrders,
    maintenanceOrders,
    deliveryOrders,
    internalMessages,
    activities,

    // CRUD operations
    addDevice,
    updateDevice,
    deleteDevice,
    addSale,
    updateSale,
    deleteSale,
    addSupplyOrder,
    updateSupplyOrder,
    deleteSupplyOrder,
    addReturn,
    updateReturn,
    deleteReturn,
    addClient,
    updateClient,
    deleteClient,
    addSupplier,
    updateSupplier,
    deleteSupplier,
    addContact,
    addUser,
    updateUser,
    deleteUser,
    addWarehouse,
    updateWarehouse,
    deleteWarehouse,

    // Utility functions
    getDeviceById,
    getWarehouseById,
    getClientById,
    getSupplierById,
    getUserById,

    // Activity logging
    addActivity,

    // System settings
    systemSettings,
    updateSystemSettings,

    // Legacy features
    maintenanceReceiptOrders,
    stocktakes,
    loadDataFromAPIs
  };

  return (
    <StoreContext.Provider value={contextValue}>
      {children}
    </StoreContext.Provider>
  );
};
