import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import type { MaintenanceOrder } from "@/lib/types";

interface MaintenanceOrdersListDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (order: MaintenanceOrder) => void;
}

export function MaintenanceOrdersListDialog({
  isOpen,
  onOpenChange,
  onSelect
}: MaintenanceOrdersListDialogProps) {
  const [orders, setOrders] = useState<MaintenanceOrder[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const loadOrders = async (pageNum: number, append: boolean = false) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/maintenance/orders?page=${pageNum}&limit=15`);
      const data = await response.json();

      if (append) {
        setOrders(prev => [...prev, ...data.data]);
      } else {
        setOrders(data.data);
      }
      
      setHasMore(data.hasMore);
      setPage(pageNum);
    } catch (error) {
      console.error("Error loading maintenance orders:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load initial data when dialog opens
  useState(() => {
    if (isOpen) {
      loadOrders(1);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>الأوامر السابقة - نظام الصيانة</DialogTitle>
        </DialogHeader>

        <div className="border rounded-lg overflow-hidden max-h-96 overflow-y-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الأمر</TableHead>
                <TableHead>المستودع</TableHead>
                <TableHead>المنشئ</TableHead>
                <TableHead>عدد الأجهزة</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span className="mr-2">جاري التحميل...</span>
                      </div>
                    ) : (
                      <span className="text-gray-500">لا توجد أوامر سابقة</span>
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>{order.orderNumber}</TableCell>
                    <TableCell>{order.warehouse?.name}</TableCell>
                    <TableCell>{order.employeeName}</TableCell>
                    <TableCell>{order.items?.length || 0}</TableCell>
                    <TableCell>
                      {new Date(order.date).toLocaleDateString("ar-SA")}
                    </TableCell>
                    <TableCell>
                      <Button
                        onClick={() => onSelect(order)}
                        size="sm"
                        variant="outline"
                      >
                        تحميل
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {hasMore && (
          <div className="flex justify-center mt-4">
            <Button
              onClick={() => loadOrders(page + 1, true)}
              disabled={isLoading}
              variant="outline"
              className="px-6"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 ml-2"></div>
                  جاري التحميل...
                </>
              ) : (
                <>
                  <Plus className="ml-2 h-4 w-4" />
                  تحميل المزيد (15 أمر)
                </>
              )}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
